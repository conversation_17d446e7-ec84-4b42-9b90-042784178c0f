{"name": "kuzumem-mcp", "version": "3.0.0", "description": "MCP tool for distributed graph memory bank storage in KuzuDB for coding agents", "main": "dist/index.js", "bin": {"KuzuMem-MCP": "dist/cli/index.js"}, "scripts": {"build": "tsc", "start:stdio": "node dist/src/mcp-stdio-server.js", "start:httpstream": "node dist/src/mcp-httpstream-server.js", "cli": "ts-node src/cli/index.ts", "test": "jest", "test:e2e": "pnpm build && jest --config ./jest.e2e.config.js --runInBand", "test:e2e:stdio": "pnpm build && jest --config ./jest.e2e.config.js --runInBand --testPathPattern=stdio-server.e2e.test.ts", "test:e2e:httpstream": "pnpm build && jest --config ./jest.e2e.config.js --runInBand --testNamePattern=httpstream-server", "test:all": "npm run test && npm run test:e2e", "lint": "eslint . --ext .ts", "format": "prettier --write .", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\" --quiet"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.3", "@types/node": "^22.15.17", "commander": "^11.1.0", "dotenv": "^16.5.0", "kuzu": "^0.10.0", "node-fetch": "^2.7.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "yaml": "^2.7.1", "zod": "^3.24.1"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node-fetch": "^2.6.4", "@types/pino": "^7.0.4", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.5", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsx": "^4.20.3", "typescript": "^5.8.3"}}
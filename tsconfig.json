{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "outDir": "dist", "sourceMap": true, "declaration": true, "resolveJsonModule": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "downlevelIteration": true, "types": ["node", "jest"], "typeRoots": ["./node_modules/@types"]}, "include": ["src/**/*", "jest.config.js", "jest.e2e.config.js"], "exclude": ["node_modules"]}
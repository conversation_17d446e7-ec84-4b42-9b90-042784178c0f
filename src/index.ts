/**
 * KuzuMem MCP - Main Entry Point
 *
 * This package provides MCP (Model Context Protocol) tools for distributed
 * graph memory bank storage using KuzuDB.
 *
 * Available servers:
 * - STDIO Server: npm run start:stdio
 * - HTTP Stream Server: npm run start:httpstream
 * - CLI Tool: npm run cli
 *
 * For more information, see README.md
 */

console.log('KuzuMem MCP - Use specific server commands:');
console.log('  - STDIO Server: npm run start:stdio');
console.log('  - HTTP Stream Server: npm run start:httpstream');
console.log('  - CLI Tool: npm run cli');
console.log('  - See README.md for more information');
